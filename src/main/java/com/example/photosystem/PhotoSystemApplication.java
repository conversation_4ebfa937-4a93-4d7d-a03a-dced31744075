package com.example.photosystem;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.example.photosystem.config.StorageProperties;

/**
 * 照片上传下载系统应用程序入口类
 */
@SpringBootApplication
@EnableCaching
@EnableScheduling
@EnableConfigurationProperties(StorageProperties.class)
public class PhotoSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(PhotoSystemApplication.class, args);
    }
}
