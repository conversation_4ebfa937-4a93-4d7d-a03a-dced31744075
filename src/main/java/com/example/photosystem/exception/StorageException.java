package com.example.photosystem.exception;

/**
 * 存储异常类
 * 用于处理文件存储过程中的异常
 */
public class StorageException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     * @param message 异常信息
     */
    public StorageException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * @param message 异常信息
     * @param cause 异常原因
     */
    public StorageException(String message, Throwable cause) {
        super(message, cause);
    }
}
