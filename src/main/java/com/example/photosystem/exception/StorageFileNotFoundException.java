package com.example.photosystem.exception;

/**
 * 存储文件未找到异常类
 * 用于处理请求的文件不存在的情况
 */
public class StorageFileNotFoundException extends StorageException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     * @param message 异常信息
     */
    public StorageFileNotFoundException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * @param message 异常信息
     * @param cause 异常原因
     */
    public StorageFileNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
