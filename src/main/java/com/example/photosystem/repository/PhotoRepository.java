package com.example.photosystem.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.example.photosystem.entity.Photo;

/**
 * 照片存储库接口
 */
@Repository
public interface PhotoRepository extends JpaRepository<Photo, Long> {

    /**
     * 根据存储文件名查找照片
     * @param storedFilename 存储文件名
     * @return 照片实体
     */
    Optional<Photo> findByStoredFilename(String storedFilename);
    
    /**
     * 根据上传者查找照片
     * @param uploadedBy 上传者用户名
     * @param pageable 分页参数
     * @return 照片分页结果
     */
    Page<Photo> findByUploadedBy(String uploadedBy, Pageable pageable);
    
    /**
     * 查找公开访问的照片
     * @param pageable 分页参数
     * @return 照片分页结果
     */
    Page<Photo> findByPublicAccessTrue(Pageable pageable);
    
    /**
     * 根据内容类型查找照片
     * @param contentType 内容类型
     * @param pageable 分页参数
     * @return 照片分页结果
     */
    Page<Photo> findByContentType(String contentType, Pageable pageable);
    
    /**
     * 根据描述信息模糊查询照片
     * @param description 描述信息
     * @param pageable 分页参数
     * @return 照片分页结果
     */
    Page<Photo> findByDescriptionContaining(String description, Pageable pageable);
    
    /**
     * 查询最近上传的照片
     * @param limit 限制数量
     * @return 照片列表
     */
    @Query("SELECT p FROM Photo p ORDER BY p.uploadTime DESC")
    List<Photo> findRecentUploads(Pageable pageable);
    
    /**
     * 查询下载次数最多的照片
     * @param pageable 分页参数
     * @return 照片分页结果
     */
    Page<Photo> findAllByOrderByDownloadCountDesc(Pageable pageable);
}
