package com.example.photosystem.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.example.photosystem.config.StorageProperties;

import lombok.extern.slf4j.Slf4j;

/**
 * 安全工具类
 * 提供文件安全处理、令牌生成和验证等功能
 */
@Component
@Slf4j
public class SecurityUtils {

    @Autowired
    private StorageProperties storageProperties;
    
    // 安全密钥，实际应用中应当从配置中读取或使用更安全的方式存储
    private static final String SECRET_KEY = "photo-system-secret-key-2025";
    
    /**
     * 生成防盗链令牌
     * @param filename 文件名
     * @return 令牌字符串
     */
    public String generateToken(String filename) {
        long expiry = Instant.now().getEpochSecond() + storageProperties.getAntiHotlinking().getTokenValidity();
        String data = filename + ":" + expiry + ":" + SECRET_KEY;
        String hash = sha256(data);
        String token = Base64.getUrlEncoder().encodeToString((expiry + ":" + hash).getBytes(StandardCharsets.UTF_8));
        return token;
    }
    
    /**
     * 验证防盗链令牌
     * @param token 令牌
     * @param filename 文件名
     * @return 是否有效
     */
    public boolean validateToken(String token, String filename) {
        try {
            String decoded = new String(Base64.getUrlDecoder().decode(token), StandardCharsets.UTF_8);
            String[] parts = decoded.split(":", 2);
            if (parts.length != 2) {
                return false;
            }
            
            long expiry = Long.parseLong(parts[0]);
            String hash = parts[1];
            
            // 检查是否过期
            if (Instant.now().getEpochSecond() > expiry) {
                return false;
            }
            
            // 验证哈希
            String data = filename + ":" + expiry + ":" + SECRET_KEY;
            String expectedHash = sha256(data);
            return expectedHash.equals(hash);
        } catch (Exception e) {
            log.error("令牌验证失败", e);
            return false;
        }
    }
    
    /**
     * 安全处理文件名，防止路径遍历攻击
     * @param filename 原始文件名
     * @return 安全处理后的文件名
     */
    public String sanitizeFilename(String filename) {
        // 移除路径分隔符和其他危险字符
        return filename.replaceAll("[\\\\/:*?\"<>|]", "_")
                      .replaceAll("\\.\\.", "_")
                      .replaceAll("\\s+", "_");
    }
    
    /**
     * 计算SHA-256哈希值
     * @param input 输入字符串
     * @return 哈希值
     */
    private String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
}
