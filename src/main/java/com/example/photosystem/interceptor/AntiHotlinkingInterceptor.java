package com.example.photosystem.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.example.photosystem.config.StorageProperties;
import com.example.photosystem.util.SecurityUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 防盗链拦截器
 * 用于防止外部网站直接引用本站图片资源
 */
@Component
@Slf4j
public class AntiHotlinkingInterceptor implements HandlerInterceptor {

    @Autowired
    private StorageProperties storageProperties;
    
    @Autowired
    private SecurityUtils securityUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果未启用防盗链，直接放行
        if (!storageProperties.getAntiHotlinking().isEnabled()) {
            return true;
        }
        
        // 获取请求的Referer
        String referer = request.getHeader("Referer");
        
        // 检查是否有token参数（带token的请求允许访问）
        String token = request.getParameter("token");
        if (token != null && !token.isEmpty()) {
            String filename = request.getRequestURI().substring(request.getRequestURI().lastIndexOf("/") + 1);
            if (securityUtils.validateToken(token, filename)) {
                return true;
            }
        }
        
        // 如果没有Referer或者Referer是允许的域名，则允许访问
        if (referer == null || isAllowedReferer(referer)) {
            return true;
        }
        
        // 否则拒绝访问
        log.warn("检测到可能的盗链: {}, Referer: {}", request.getRequestURI(), referer);
        response.sendError(HttpServletResponse.SC_FORBIDDEN, "禁止访问");
        return false;
    }
    
    /**
     * 检查Referer是否是允许的域名
     */
    private boolean isAllowedReferer(String referer) {
        return storageProperties.getAntiHotlinking().getAllowedDomainsList().stream()
                .anyMatch(domain -> referer.contains(domain));
    }
}
