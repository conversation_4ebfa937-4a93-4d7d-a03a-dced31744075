package com.example.photosystem.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 照片实体类
 */
@Entity
@Table(name = "photos")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Photo {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 原始文件名
     */
    @Column(nullable = false)
    private String originalFilename;
    
    /**
     * 存储文件名（唯一生成的文件名）
     */
    @Column(nullable = false, unique = true)
    private String storedFilename;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型（MIME类型）
     */
    @Column(nullable = false)
    private String contentType;
    
    /**
     * 上传时间
     */
    @Column(nullable = false)
    private LocalDateTime uploadTime;
    
    /**
     * 上传者用户名
     */
    @Column(nullable = false)
    private String uploadedBy;
    
    /**
     * 图片宽度（像素）
     */
    private Integer width;
    
    /**
     * 图片高度（像素）
     */
    private Integer height;
    
    /**
     * 是否已生成缩略图
     */
    private boolean thumbnailGenerated;
    
    /**
     * 是否公开访问
     */
    private boolean publicAccess;
    
    /**
     * 下载次数
     */
    private Integer downloadCount;
    
    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessTime;
    
    /**
     * 描述信息
     */
    @Column(length = 500)
    private String description;
}
