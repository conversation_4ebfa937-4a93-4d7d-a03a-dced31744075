package com.example.photosystem.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

/**
 * 文件存储配置属性类
 * 从application.yml中读取photo.storage前缀的配置
 */
@ConfigurationProperties(prefix = "photo.storage")
@Data
public class StorageProperties {
    
    /**
     * 文件存储根路径
     */
    private String location;
    
    /**
     * 允许的文件类型，以逗号分隔
     */
    private String allowedTypes;
    
    /**
     * 缓存目录
     */
    private String cacheDir;
    
    /**
     * 临时目录
     */
    private String tempDir;
    
    /**
     * 清理任务配置
     */
    private Cleanup cleanup = new Cleanup();
    
    /**
     * 图片压缩质量（0-1之间）
     */
    private double compressionQuality;
    
    /**
     * 防盗链配置
     */
    private AntiHotlinking antiHotlinking = new AntiHotlinking();
    
    /**
     * 清理任务配置内部类
     */
    @Data
    public static class Cleanup {
        /**
         * 临时文件清理天数
         */
        private int tempFilesOlderThan;
        
        /**
         * 缓存文件清理天数
         */
        private int cacheFilesOlderThan;
    }
    
    /**
     * 防盗链配置内部类
     */
    @Data
    public static class AntiHotlinking {
        /**
         * 是否启用防盗链
         */
        private boolean enabled;
        
        /**
         * 允许的域名列表
         */
        private String allowedDomains;
        
        /**
         * 令牌有效期（秒）
         */
        private long tokenValidity;
        
        /**
         * 获取允许的域名列表
         */
        public List<String> getAllowedDomainsList() {
            return List.of(allowedDomains.split(","));
        }
    }
}
