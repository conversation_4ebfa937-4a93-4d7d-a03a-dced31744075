package com.example.photosystem.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.example.photosystem.interceptor.AntiHotlinkingInterceptor;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private StorageProperties storageProperties;
    
    @Autowired
    private AntiHotlinkingInterceptor antiHotlinkingInterceptor;

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件的访问路径
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + storageProperties.getLocation() + "/");
    }
    
    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加防盗链拦截器
        if (storageProperties.getAntiHotlinking().isEnabled()) {
            registry.addInterceptor(antiHotlinkingInterceptor)
                   .addPathPatterns("/api/photos/view/**", "/api/photos/thumbnail/**");
        }
    }
}
