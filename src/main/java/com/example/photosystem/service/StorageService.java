package com.example.photosystem.service;

import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Stream;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import com.example.photosystem.entity.Photo;
import com.example.photosystem.exception.StorageException;

/**
 * 存储服务接口
 * 定义文件存储相关的操作
 */
public interface StorageService {

    /**
     * 初始化存储系统
     * 创建必要的目录结构
     */
    void init();

    /**
     * 存储单个文件
     * @param file 上传的文件
     * @param username 上传用户
     * @param isPublic 是否公开访问
     * @param description 文件描述
     * @return 保存的照片实体
     */
    Photo store(MultipartFile file, String username, boolean isPublic, String description);

    /**
     * 存储多个文件
     * @param files 上传的文件列表
     * @param username 上传用户
     * @param isPublic 是否公开访问
     * @param description 文件描述
     * @return 保存的照片实体列表
     */
    List<Photo> storeAll(List<MultipartFile> files, String username, boolean isPublic, String description);

    /**
     * 加载文件作为资源
     * @param filename 存储的文件名
     * @return 文件资源
     */
    Resource loadAsResource(String filename);

    /**
     * 获取文件路径
     * @param filename 存储的文件名
     * @return 文件路径
     */
    Path load(String filename);

    /**
     * 获取所有文件路径
     * @return 文件路径流
     */
    Stream<Path> loadAll();

    /**
     * 删除文件
     * @param filename 存储的文件名
     */
    void delete(String filename);

    /**
     * 生成缩略图
     * @param photo 照片实体
     * @return 缩略图路径
     */
    Path generateThumbnail(Photo photo);

    /**
     * 加载缩略图作为资源
     * @param filename 存储的文件名
     * @return 缩略图资源
     */
    Resource loadThumbnailAsResource(String filename);

    /**
     * 验证文件类型
     * @param file 上传的文件
     * @return 是否为有效的图片文件
     */
    boolean validateFileType(MultipartFile file);

    /**
     * 获取文件的输入流
     * @param filename 存储的文件名
     * @return 文件输入流
     */
    InputStream getFileInputStream(String filename) throws StorageException;

    /**
     * 清理临时文件
     */
    void cleanupTempFiles();

    /**
     * 清理缓存文件
     */
    void cleanupCacheFiles();
    
    /**
     * 压缩图片
     * @param photo 照片实体
     * @param quality 压缩质量(0-1)
     * @return 压缩后的文件路径
     */
    Path compressImage(Photo photo, double quality);
    
    /**
     * 获取存储使用情况
     * @return 存储使用信息
     */
    StorageStats getStorageStats();
    
    /**
     * 存储统计信息类
     */
    class StorageStats {
        private long totalSpace;
        private long usedSpace;
        private long freeSpace;
        private int fileCount;
        
        // Getters and setters
        public long getTotalSpace() { return totalSpace; }
        public void setTotalSpace(long totalSpace) { this.totalSpace = totalSpace; }
        
        public long getUsedSpace() { return usedSpace; }
        public void setUsedSpace(long usedSpace) { this.usedSpace = usedSpace; }
        
        public long getFreeSpace() { return freeSpace; }
        public void setFreeSpace(long freeSpace) { this.freeSpace = freeSpace; }
        
        public int getFileCount() { return fileCount; }
        public void setFileCount(int fileCount) { this.fileCount = fileCount; }
    }
}
