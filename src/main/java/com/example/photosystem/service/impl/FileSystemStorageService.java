package com.example.photosystem.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import javax.imageio.ImageIO;

import org.apache.commons.io.FilenameUtils;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import com.example.photosystem.config.StorageProperties;
import com.example.photosystem.entity.Photo;
import com.example.photosystem.exception.StorageException;
import com.example.photosystem.exception.StorageFileNotFoundException;
import com.example.photosystem.repository.PhotoRepository;
import com.example.photosystem.service.StorageService;
import com.example.photosystem.util.SecurityUtils;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

/**
 * 文件系统存储服务实现类
 */
@Service
@Slf4j
public class FileSystemStorageService implements StorageService {

    private final Path rootLocation;
    private final Path cacheLocation;
    private final Path tempLocation;
    private final List<String> allowedTypes;
    private final Tika tika;
    
    @Autowired
    private PhotoRepository photoRepository;
    
    @Autowired
    private SecurityUtils securityUtils;

    @Autowired
    public FileSystemStorageService(StorageProperties properties) {
        this.rootLocation = Paths.get(properties.getLocation());
        this.cacheLocation = Paths.get(properties.getCacheDir());
        this.tempLocation = Paths.get(properties.getTempDir());
        this.allowedTypes = List.of(properties.getAllowedTypes().split(","));
        this.tika = new Tika();
    }

    @Override
    public void init() {
        try {
            Files.createDirectories(rootLocation);
            Files.createDirectories(cacheLocation);
            Files.createDirectories(tempLocation);
            log.info("已创建存储目录: {}, {}, {}", rootLocation, cacheLocation, tempLocation);
        } catch (IOException e) {
            throw new StorageException("无法初始化存储目录", e);
        }
    }

    @Override
    public Photo store(MultipartFile file, String username, boolean isPublic, String description) {
        try {
            if (file.isEmpty()) {
                throw new StorageException("无法存储空文件");
            }
            
            // 验证文件类型
            if (!validateFileType(file)) {
                throw new StorageException("不支持的文件类型");
            }
            
            // 获取文件扩展名
            String originalFilename = file.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);
            
            // 生成唯一文件名
            String storedFilename = UUID.randomUUID().toString() + "." + extension;
            
            // 安全处理文件名
            storedFilename = securityUtils.sanitizeFilename(storedFilename);
            
            // 获取文件路径
            Path destinationFile = this.rootLocation.resolve(storedFilename);
            
            // 确保目标路径在存储目录内
            if (!destinationFile.getParent().equals(this.rootLocation.toAbsolutePath())) {
                throw new StorageException("无法将文件存储到当前目录之外");
            }
            
            // 保存文件
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, destinationFile, StandardCopyOption.REPLACE_EXISTING);
            }
            
            // 获取图片尺寸
            BufferedImage bufferedImage = ImageIO.read(destinationFile.toFile());
            Integer width = null;
            Integer height = null;
            if (bufferedImage != null) {
                width = bufferedImage.getWidth();
                height = bufferedImage.getHeight();
            }
            
            // 创建照片实体
            Photo photo = Photo.builder()
                    .originalFilename(originalFilename)
                    .storedFilename(storedFilename)
                    .fileSize(file.getSize())
                    .contentType(file.getContentType())
                    .uploadTime(LocalDateTime.now())
                    .uploadedBy(username)
                    .width(width)
                    .height(height)
                    .thumbnailGenerated(false)
                    .publicAccess(isPublic)
                    .downloadCount(0)
                    .description(description)
                    .build();
            
            // 保存照片信息到数据库
            Photo savedPhoto = photoRepository.save(photo);
            
            // 异步生成缩略图
            new Thread(() -> {
                try {
                    generateThumbnail(savedPhoto);
                    savedPhoto.setThumbnailGenerated(true);
                    photoRepository.save(savedPhoto);
                } catch (Exception e) {
                    log.error("生成缩略图失败: {}", savedPhoto.getStoredFilename(), e);
                }
            }).start();
            
            log.info("成功存储文件: {}", storedFilename);
            return savedPhoto;
        } catch (IOException e) {
            throw new StorageException("存储文件失败", e);
        }
    }

    @Override
    public List<Photo> storeAll(List<MultipartFile> files, String username, boolean isPublic, String description) {
        List<Photo> savedPhotos = new ArrayList<>();
        for (MultipartFile file : files) {
            savedPhotos.add(store(file, username, isPublic, description));
        }
        return savedPhotos;
    }

    @Override
    public Stream<Path> loadAll() {
        try {
            return Files.walk(this.rootLocation, 1)
                    .filter(path -> !path.equals(this.rootLocation))
                    .map(this.rootLocation::relativize);
        } catch (IOException e) {
            throw new StorageException("读取存储的文件失败", e);
        }
    }

    @Override
    public Path load(String filename) {
        return rootLocation.resolve(filename);
    }

    @Override
    public Resource loadAsResource(String filename) {
        try {
            Path file = load(filename);
            Resource resource = new UrlResource(file.toUri());
            if (resource.exists() || resource.isReadable()) {
                return resource;
            } else {
                throw new StorageFileNotFoundException("无法读取文件: " + filename);
            }
        } catch (MalformedURLException e) {
            throw new StorageFileNotFoundException("无法读取文件: " + filename, e);
        }
    }

    @Override
    public void delete(String filename) {
        try {
            Path file = load(filename);
            Files.deleteIfExists(file);
            
            // 删除缩略图
            Path thumbnailFile = Paths.get(cacheLocation.toString(), "thumbnail_" + filename);
            Files.deleteIfExists(thumbnailFile);
            
            log.info("成功删除文件: {}", filename);
        } catch (IOException e) {
            throw new StorageException("删除文件失败: " + filename, e);
        }
    }

    @Override
    public Path generateThumbnail(Photo photo) {
        try {
            Path originalFile = load(photo.getStoredFilename());
            String extension = FilenameUtils.getExtension(photo.getStoredFilename());
            Path thumbnailFile = Paths.get(cacheLocation.toString(), "thumbnail_" + photo.getStoredFilename());
            
            // 生成缩略图
            Thumbnails.of(originalFile.toFile())
                    .size(200, 200)
                    .keepAspectRatio(true)
                    .outputFormat(extension)
                    .toFile(thumbnailFile.toFile());
            
            log.info("成功生成缩略图: {}", thumbnailFile);
            return thumbnailFile;
        } catch (IOException e) {
            throw new StorageException("生成缩略图失败", e);
        }
    }

    @Override
    public Resource loadThumbnailAsResource(String filename) {
        try {
            Path file = Paths.get(cacheLocation.toString(), "thumbnail_" + filename);
            Resource resource = new UrlResource(file.toUri());
            if (resource.exists() || resource.isReadable()) {
                return resource;
            } else {
                // 如果缩略图不存在，尝试生成
                Photo photo = photoRepository.findByStoredFilename(filename)
                        .orElseThrow(() -> new StorageFileNotFoundException("找不到照片记录: " + filename));
                Path thumbnailPath = generateThumbnail(photo);
                return new UrlResource(thumbnailPath.toUri());
            }
        } catch (MalformedURLException e) {
            throw new StorageFileNotFoundException("无法读取缩略图: " + filename, e);
        }
    }

    @Override
    public boolean validateFileType(MultipartFile file) {
        try {
            String detectedType = tika.detect(file.getInputStream());
            return allowedTypes.stream().anyMatch(detectedType::startsWith);
        } catch (IOException e) {
            throw new StorageException("验证文件类型失败", e);
        }
    }

    @Override
    public InputStream getFileInputStream(String filename) throws StorageException {
        try {
            Path file = load(filename);
            return Files.newInputStream(file);
        } catch (IOException e) {
            throw new StorageException("无法获取文件输入流: " + filename, e);
        }
    }

    @Override
    public void cleanupTempFiles() {
        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minus(
                    1, ChronoUnit.DAYS);
            
            Files.walk(tempLocation)
                 .filter(Files::isRegularFile)
                 .filter(path -> {
                     try {
                         return Files.getLastModifiedTime(path).toInstant()
                                 .isBefore(cutoffDate.toInstant(java.time.ZoneOffset.UTC));
                     } catch (IOException e) {
                         return false;
                     }
                 })
                 .forEach(path -> {
                     try {
                         Files.delete(path);
                         log.info("已删除临时文件: {}", path);
                     } catch (IOException e) {
                         log.error("删除临时文件失败: {}", path, e);
                     }
                 });
        } catch (IOException e) {
            log.error("清理临时文件失败", e);
        }
    }

    @Override
    public void cleanupCacheFiles() {
        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minus(
                    7, ChronoUnit.DAYS);
            
            Files.walk(cacheLocation)
                 .filter(Files::isRegularFile)
                 .filter(path -> {
                     try {
                         return Files.getLastModifiedTime(path).toInstant()
                                 .isBefore(cutoffDate.toInstant(java.time.ZoneOffset.UTC));
                     } catch (IOException e) {
                         return false;
                     }
                 })
                 .forEach(path -> {
                     try {
                         Files.delete(path);
                         log.info("已删除缓存文件: {}", path);
                     } catch (IOException e) {
                         log.error("删除缓存文件失败: {}", path, e);
                     }
                 });
        } catch (IOException e) {
            log.error("清理缓存文件失败", e);
        }
    }

    @Override
    public Path compressImage(Photo photo, double quality) {
        try {
            Path originalFile = load(photo.getStoredFilename());
            String extension = FilenameUtils.getExtension(photo.getStoredFilename());
            String compressedFilename = "compressed_" + photo.getStoredFilename();
            Path compressedFile = Paths.get(cacheLocation.toString(), compressedFilename);
            
            // 压缩图片
            Thumbnails.of(originalFile.toFile())
                    .scale(1.0)
                    .outputQuality(quality)
                    .outputFormat(extension)
                    .toFile(compressedFile.toFile());
            
            log.info("成功压缩图片: {}", compressedFile);
            return compressedFile;
        } catch (IOException e) {
            throw new StorageException("压缩图片失败", e);
        }
    }

    @Override
    public StorageStats getStorageStats() {
        File rootDir = rootLocation.toFile();
        
        StorageStats stats = new StorageStats();
        stats.setTotalSpace(rootDir.getTotalSpace());
        stats.setFreeSpace(rootDir.getFreeSpace());
        stats.setUsedSpace(rootDir.getTotalSpace() - rootDir.getFreeSpace());
        
        try {
            long count = Files.walk(rootLocation)
                    .filter(Files::isRegularFile)
                    .count();
            stats.setFileCount((int) count);
        } catch (IOException e) {
            log.error("获取文件数量失败", e);
            stats.setFileCount(-1);
        }
        
        return stats;
    }
}
