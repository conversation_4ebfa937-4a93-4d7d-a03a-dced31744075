package com.example.service;

import com.example.entity.Photo;
import com.example.repository.PhotoRepository;
import com.example.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PhotoService {

    private final PhotoRepository photoRepository;
    private final OssService ossService;
    
    @Value("${photo.upload.allowed-types}")
    private String allowedTypes;

    @Value("${photo.upload.max-size}")
    private long maxSize;
    
    @Value("${aliyun.oss.urlExpiration}")
    private int urlExpiration;

    @Transactional
    public Photo upload(MultipartFile file) throws IOException {
        // 验证文件
        validateFile(file);

        // 获取文件扩展名
        String fileExtension = FileUtils.getFileExtension(file.getOriginalFilename());
        
        // 上传到阿里云OSS
        String fileName = ossService.uploadFile(file, fileExtension);

        // 创建Photo实体
        Photo photo = new Photo();
        photo.setOriginalName(file.getOriginalFilename());
        photo.setFileName(fileName);
        photo.setContentType(file.getContentType());
        photo.setSize(file.getSize());
        photo.setUploadTime(LocalDateTime.now());
        photo.setUploadIp(FileUtils.getRemoteIp());
        photo.setStoragePath(fileName); // 存储OSS中的文件名
        // 假设Photo实体中的方法是setPublic而不是setIsPublic
        photo.setPublic(false);
        photo.setDownloadCount(0);
        photo.setDeleted(false);

        return photoRepository.save(photo);
    }

    @Transactional
    public List<Photo> uploadMultiple(MultipartFile[] files) throws IOException {
        return java.util.Arrays.stream(files)
                .map(file -> {
                    try {
                        return upload(file);
                    } catch (IOException e) {
                        throw new RuntimeException("Failed to upload file: " + file.getOriginalFilename(), e);
                    }
                })
                .toList();
    }

    public byte[] download(String fileName) throws IOException {
        Photo photo = photoRepository.findByFileNameAndDeletedFalse(fileName)
                .orElseThrow(() -> new RuntimeException("File not found: " + fileName));

        // 更新下载计数
        photo.setDownloadCount(photo.getDownloadCount() + 1);
        photoRepository.save(photo);

        // 从阿里云OSS下载
        return ossService.downloadFile(photo.getStoragePath());
    }

    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("File cannot be empty");
        }

        if (file.getSize() > maxSize) {
            throw new RuntimeException("File size exceeds limit");
        }

        String contentType = file.getContentType();
        if (contentType == null || !FileUtils.isImage(contentType)) {
            throw new RuntimeException("Invalid file type");
        }
    }

    public List<Photo> getPublicPhotos() {
        return photoRepository.findByDeletedFalseAndIsPublicTrue();
    }

    public void deletePhoto(Long id) {
        Photo photo = photoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Photo not found"));
        photo.setDeleted(true);
        photoRepository.save(photo);
        
        // 从阿里云OSS删除文件
        try {
            ossService.deleteFile(photo.getStoragePath());
        } catch (Exception e) {
            // 记录日志，但不影响事务
        }
        /**
     * 获取照片的访问URL
     * 
     * @param fileName 文件名
     * @return 访问URL
     */
    public String getPhotoUrl(String fileName) {
        Photo photo = photoRepository.findByFileNameAndDeletedFalse(fileName)
                .orElseThrow(() -> new RuntimeException("File not found: " + fileName));
        
        return ossService.getFileUrl(photo.getStoragePath(), urlExpiration);
    }
}
}
