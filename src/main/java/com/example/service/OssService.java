package com.example.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.example.config.OssConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

/**
 * 阿里云OSS服务类
 */
@Service
@RequiredArgsConstructor
public class OssService {

    private final OSS ossClient;
    private final OssConfig ossConfig;

    /**
     * 上传文件到OSS
     *
     * @param file 文件
     * @param fileExtension 文件扩展名
     * @return OSS中的文件名
     */
    public String uploadFile(MultipartFile file, String fileExtension) throws IOException {
        // 生成唯一文件名
        String fileName = UUID.randomUUID().toString();
        if (fileExtension != null && !fileExtension.isEmpty()) {
            fileName = fileName + "." + fileExtension;
        }

        // 设置元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());
        metadata.setContentLength(file.getSize());

        // 上传文件
        ossClient.putObject(ossConfig.getBucketName(), fileName, file.getInputStream(), metadata);

        return fileName;
    }

    /**
     * 从OSS下载文件
     *
     * @param fileName OSS中的文件名
     * @return 文件字节数组
     */
    public byte[] downloadFile(String fileName) throws IOException {
        OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), fileName);
        try (InputStream inputStream = ossObject.getObjectContent()) {
            return inputStream.readAllBytes();
        }
    }

    /**
     * 删除OSS中的文件
     *
     * @param fileName OSS中的文件名
     */
    public void deleteFile(String fileName) {
        ossClient.deleteObject(ossConfig.getBucketName(), fileName);
    }

    /**
     * 获取文件访问URL（带有效期）
     *
     * @param fileName OSS中的文件名
     * @param expireInSeconds URL有效期（秒）
     * @return 访问URL
     */
    public String getFileUrl(String fileName, int expireInSeconds) {
        Date expiration = new Date(System.currentTimeMillis() + expireInSeconds * 1000L);
        URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), fileName, expiration);
        return url.toString();
    }

    /**
     * 检查文件是否存在
     *
     * @param fileName OSS中的文件名
     * @return 是否存在
     */
    public boolean doesFileExist(String fileName) {
        return ossClient.doesObjectExist(ossConfig.getBucketName(), fileName);
    }
}
