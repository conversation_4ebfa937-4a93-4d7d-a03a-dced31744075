server:
  port: 8080
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

spring:
  profiles:
    active: dev

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com # 请替换为您的OSS endpoint
    accessKeyId: ${OSS_ACCESS_KEY_ID} # 从环境变量获取，请设置您的AccessKeyId
    accessKeySecret: ${OSS_ACCESS_KEY_SECRET} # 从环境变量获取，请设置您的AccessKeySecret
    bucketName: ${OSS_BUCKET_NAME} # 从环境变量获取，请设置您的Bucket名称
    urlExpiration: 3600 # URL有效期（秒）

photo:
  upload:
    path: ${user.home}/photo-upload # 保留本地路径作为备份
    allowed-types: png,jpg,jpeg,gif
    max-size: 10485760 # 10MB
  cache:
    enabled: true
    max-size: 100 # 最大缓存文件数
    timeout: 3600 # 缓存过期时间（秒）

security:
  basic:
    enabled: true
    password: ${RANDOM_PASSWORD}

logging:
  level:
    com.example: DEBUG
    org.springframework.web: INFO
